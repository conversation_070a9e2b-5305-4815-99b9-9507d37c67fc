package main

import (
	"context"
	"fmt"
	"strings"
	"time"

	gohelper "github.com/real-rm/gohelper"
	levelStore "github.com/real-rm/golevelstore"
	golog "github.com/real-rm/golog"
	gospeedmeter "github.com/real-rm/gospeedmeter"
	gostreaming "github.com/real-rm/gostreaming"
	"go.mongodb.org/mongo-driver/bson"

	goresodownload "github.com/real-rm/goresodownload"
)

// migrateDisk performs migration for the specified disk using streaming
func migrateDisk(disk string, config Config) error {
	golog.Info("Starting migration", "disk", disk, "dryRun", config.DryRun)

	// Initialize speed meter for performance tracking
	speedMeter := gospeedmeter.NewSpeedMeter(gospeedmeter.SpeedMeterOptions{})

	// Use global context for graceful shutdown
	ctx := globalCtx

	// Get cursor for streaming
	cursor, err := getPropertiesCursor(config)
	if err != nil {
		return fmt.Errorf("failed to get properties cursor: %w", err)
	}

	// Process properties using streaming
	streamOpts := &gostreaming.StreamingOptions{
		Stream:        cursor,
		High:          1,    // Process up to 1 properties concurrently
		SpeedInterval: 5000, // Log speed every 5 seconds
		Verbose:       2,
		Process: func(item interface{}) error {
			return processPropertyItem(item, config, speedMeter)
		},
		End: func(err error) {
			// Check if error is not nil and has actual content
			if err != nil && err.Error() != "" {
				if err == context.Canceled {
					golog.Info("Stream ended due to graceful shutdown", "disk", disk, "speed", speedMeter.ToString(gospeedmeter.UnitM, nil))
				} else {
					golog.Error("Stream ended with error", "error", err, "disk", disk, "speed", speedMeter.ToString(gospeedmeter.UnitM, nil))
				}
			} else {
				golog.Info("Stream completed successfully", "disk", disk, "speed", speedMeter.ToString(gospeedmeter.UnitM, nil))
			}
		},
		Error: func(err error) {
			golog.Error("Streaming error", "error", err)
		},
	}

	// Start streaming processing
	if err := gostreaming.Streaming(ctx, streamOpts); err != nil {
		return fmt.Errorf("streaming failed: %w", err)
	}

	return nil
}

// processPropertyItem processes a single property item from the stream
func processPropertyItem(item interface{}, config Config, speedMeter *gospeedmeter.SpeedMeter) error {
	// Sleep if configured
	if config.SleepMs > 0 {
		time.Sleep(time.Duration(config.SleepMs) * time.Millisecond)
	}

	// Track processing speed
	speedMeter.Check("processed", 1)

	// Handle different possible types from MongoDB cursor
	var prop bson.M
	switch v := item.(type) {
	case bson.M:
		prop = v
	case bson.D:
		// Convert bson.D to bson.M
		prop = make(bson.M)
		for _, elem := range v {
			prop[elem.Key] = elem.Value
		}
	case map[string]interface{}:
		prop = bson.M(v)
	default:
		speedMeter.Check("errors", 1)
		golog.Error("Invalid property type", "actualType", fmt.Sprintf("%T", item), "value", item)
		return fmt.Errorf("invalid property type")
	}

	golog.Info("Processing property", "propID", prop["_id"])

	// Check if property needs processing
	if !needToProcess(prop, config.Disk) {
		golog.Info("Skipping property - already processed or no images", "propID", prop["_id"])
		speedMeter.Check("skipped", 1)
		return nil
	}

	// Process the property with graceful shutdown support
	result, err := processPropertyWithGracefulShutdown(prop, config)
	if err != nil {
		if err == context.Canceled {
			golog.Info("Graceful shutdown completed for current property", "propID", prop["_id"])
			return context.Canceled // Propagate the cancellation to stop streaming
		}

		golog.Error("Failed to process property", "propID", prop["_id"], "error", err.Error())
		speedMeter.Check("failed", 1)

		// Record failure in migration log
		migrationResult := MigrationResult{
			PropID:    fmt.Sprintf("%v", prop["_id"]),
			SrcFolder: getSrcFolderFromProp(prop),
			DstFolder: getDstFolderFromProp(prop),
			Status:    "processing_failed",
			ErrMsg:    err.Error(),
			Disk:      config.Disk,
		}

		if !config.DryRun {
			if logErr := insertMigrationLog(migrationResult); logErr != nil {
				golog.Error("Failed to insert error log", "error", logErr)
			}
		}
		return nil // Don't stop streaming on individual property failure
	}

	if result.Success > 0 {
		speedMeter.Check("success", 1)
		// Migration log will be recorded in processProperty function with more detailed status
	} else {
		speedMeter.Check("allPhotoFailed", 1)
	}

	golog.Info("Property processed",
		"propID", prop["_id"],
		"images", result.ImageCount,
		"success", result.Success,
		"failed", result.Failed,
		"srcFolder", getSrcFolder(result.ImagePaths),
		"dstFolder", getDstFolder(result.PhoP))

	return nil
}

// processPropertyWithGracefulShutdown processes a property with graceful shutdown support
func processPropertyWithGracefulShutdown(prop bson.M, config Config) (*PropertyResult, error) {
	// Check for graceful shutdown signal before starting
	select {
	case <-globalCtx.Done():
		golog.Info("Graceful shutdown requested, skipping property processing", "propID", prop["_id"])
		return nil, context.Canceled
	default:
	}

	// Process the property and allow it to complete even if shutdown signal is received
	result, err := processProperty(prop, config)

	// Check if shutdown was requested during processing
	if globalCtx.Err() == context.Canceled {
		golog.Info("Graceful shutdown completed for current property", "propID", prop["_id"])
		return result, context.Canceled
	}

	return result, err
}

// processProperty processes a single property and all its images
func processProperty(prop bson.M, config Config) (*PropertyResult, error) {
	propID := fmt.Sprintf("%v", prop["_id"])
	src, ok := prop["src"].(string)
	if !ok {
		return nil, fmt.Errorf("invalid src field")
	}

	result := &PropertyResult{
		PropID:       propID,
		Processed:    true,
		ImageCount:   0,
		Success:      0,
		Failed:       0,
		Renamed:      0,
		Copied:       0,
		HardLinked:   0,
		AlreadyExist: 0,
		Errors:       []error{},
		PhoP:         "",
		ImagePaths:   []string{},
		MainOpType:   "",
	}

	var imageInfos []*ImageInfo
	var phoLH []int32
	var tnLH int32

	// PHASE 1: Collect all image paths and resolve hash conflicts BEFORE processing files
	var imagePaths []string // [relativeOldPath, ...]
	var err error

	switch src {
	case SRC_TRB, SRC_DDF:
		imagePaths, err = collectTRBDDFImagePaths(prop)
	case SRC_OTW, SRC_CLG:
		imagePaths, err = collectOTWCLGImagePaths(prop)
	default:
		return result, fmt.Errorf("unsupported src type: %s", src)
	}

	if err != nil {
		return result, fmt.Errorf("failed to collect image paths: %w", err)
	}

	result.ImageCount = len(imagePaths)

	// Resolve hash conflicts and pre-calculate all paths BEFORE processing any files
	hashSet := make(map[int32]bool)
	imageHashMap := make(map[string]int32)  // relativePath -> uniqueHash
	imagePathMap := make(map[string]string) // relativePath -> newPath

	// Pre-extract common fields once
	sid, ok := prop["sid"].(string)
	if !ok {
		return result, fmt.Errorf("invalid sid field in property")
	}

	// Pre-calculate common path components once
	// get phoP from property
	var phoP string
	if config.Disk == "ca7" {
		// On ca7, check if we should skip phoP generation
		if goresodownload.ShouldSkipPhoPGeneration(prop) {
			// Use existing phoP from the property - direct assignment since ShouldSkipPhoPGeneration already validated it
			phoP = prop["phoP"].(string)
			golog.Info("Using existing phoP on ca7", "propID", prop["_id"], "phoP", phoP)
		} else {
			// Generate new phoP
			var err error
			golog.Warn("there is no existing phoP, generating new one", "propID", prop["_id"])
			phoP, err = generateNewPhoP(prop)
			if err != nil {
				return result, fmt.Errorf("failed to generate phoP on ca7: %w", err)
			}
		}
	} else {
		// On ca6, always generate new phoP
		var err error
		phoP, err = generateNewPhoP(prop)
		if err != nil {
			return result, fmt.Errorf("failed to generate phoP on ca6: %w", err)
		}
	}

	// Pre-calculate all paths BEFORE processing any files
	// imagePathMap: map[relativeOldPath]newPath
	for _, relativePath := range imagePaths {
		originalHash := levelStore.MurmurToInt32(relativePath)
		uniqueHash := ensureUniqueHash(originalHash, relativePath, hashSet)
		imageHashMap[relativePath] = uniqueHash
		hashSet[uniqueHash] = true

		// Pre-calculate new path using the unique hash and pre-calculated phoP
		newPath, err := buildNewPathWithHash(phoP, sid, uniqueHash)
		if err != nil {
			return result, fmt.Errorf("failed to build new path for %s: %w", relativePath, err)
		}
		imagePathMap[relativePath] = newPath
	}

	// Generate thumbnail hash for the first image
	if len(imagePaths) > 0 {
		firstImagePath := imagePaths[0]
		thumbKey := firstImagePath + "-t"
		thumbHash := levelStore.MurmurToInt32(thumbKey)
		tnLH = ensureUniqueHash(thumbHash, thumbKey, hashSet)
		hashSet[tnLH] = true
	}

	// PHASE 2: Batch process all files with pre-calculated paths and hashes
	// imagePaths: [relativeOldPath, ...]
	// imageHashMap: map[relativeOldPath]uniqueHash
	// imagePathMap: map[relativeOldPath]newPath
	batchResult, err := batchProcessImageFiles(imagePaths, imageHashMap, imagePathMap, src, propID, config)
	if err != nil {
		return result, fmt.Errorf("batch processing failed: %w", err)
	}

	// Update result with batch processing results
	imageInfos = batchResult.ImageInfos
	result.Success = batchResult.Success
	result.Failed = batchResult.Failed
	result.Errors = append(result.Errors, batchResult.Errors...)

	// Count different operation types and filter successful operations
	successfulImagePaths := make([]string, 0)
	successfulHashes := make([]int32, 0)
	successfulImageInfos := make([]*ImageInfo, 0)

	// Helper function to add successful operation
	addSuccessfulOperation := func(imgInfo *ImageInfo) {
		// Use RelativeOldPath directly from ImageInfo
		successfulImagePaths = append(successfulImagePaths, imgInfo.RelativeOldPath)
		successfulHashes = append(successfulHashes, imageHashMap[imgInfo.RelativeOldPath])
		successfulImageInfos = append(successfulImageInfos, imgInfo)
	}

	for _, imgInfo := range batchResult.ImageInfos {
		switch imgInfo.OperationType {
		case STATUS_HARD_LINKED:
			result.HardLinked++
			addSuccessfulOperation(imgInfo)
		case STATUS_COPIED:
			result.Copied++
			addSuccessfulOperation(imgInfo)
		case STATUS_ALREADY_EXISTS:
			result.AlreadyExist++
			addSuccessfulOperation(imgInfo)
		case STATUS_RENAMED:
			result.Renamed++ // Keep for backward compatibility
			addSuccessfulOperation(imgInfo)
		}
	}

	// Update phoLH to only include successfully processed images
	phoLH = successfulHashes

	// Skip all database operations in dry run mode
	if config.DryRun {
		golog.Info("Skipping database operations in dry run mode", "propID", result.PropID, "phoP", phoP, "imagePaths", successfulImagePaths)
		// Set the generated phoP and collected image paths in result
		result.PhoP = phoP
		result.ImagePaths = successfulImagePaths // Only include successfully processed images
		return result, nil
	}

	// Log information about missing files
	if len(successfulImagePaths) < len(imagePaths) {
		missingCount := len(imagePaths) - len(successfulImagePaths)
		golog.Info("Some images were missing or failed to process",
			"propID", propID,
			"totalImages", len(imagePaths),
			"successfulImages", len(successfulImagePaths),
			"missingImages", missingCount)
	}

	// Determine main operation type
	if result.HardLinked > 0 && result.Copied == 0 && result.AlreadyExist == 0 {
		result.MainOpType = "hard_linked"
	} else if result.Copied > 0 && result.HardLinked == 0 && result.AlreadyExist == 0 {
		result.MainOpType = "copied"
	} else if result.AlreadyExist > 0 && result.HardLinked == 0 && result.Copied == 0 {
		result.MainOpType = "already_exists"
	} else if result.HardLinked > 0 || result.Copied > 0 || result.AlreadyExist > 0 {
		result.MainOpType = "mixed"
	} else {
		result.MainOpType = "unknown"
	}

	// Generate thumbnail for the first image if any images were successfully processed
	// Only generate thumbnail if we have successfully processed images
	if len(successfulImagePaths) > 0 && len(imageInfos) > 0 && !config.DryRun && tnLH != 0 {
		firstImageInfo := successfulImageInfos[0]
		// Generate thumbnail relative path using the predetermined tnLH hash
		thumbnailRelativePath, err := buildNewPathWithHash(phoP, sid, tnLH)
		if err != nil {
			golog.Error("Failed to build thumbnail path with hash", "propID", result.PropID, "error", err, "phoP", phoP, "sid", sid, "tnLH", tnLH)
		} else {
			// Build full thumbnail path by adding base path and src subdirectory
			var dstBasePath string
			if config.Disk == "ca6" {
				dstBasePath = NEW_CA6_PATH
			} else {
				dstBasePath = NEW_CA7_PATH
			}
			thumbnailFullPath := fmt.Sprintf("%s/%s/%s", dstBasePath, src, thumbnailRelativePath)

			// Use the new path as source since the file has been moved
			golog.Info("Creating thumbnail", "propID", result.PropID, "source", firstImageInfo.NewPath, "thumbnail", thumbnailFullPath, "hash", tnLH)
			err := createThumbnail(firstImageInfo.NewPath, thumbnailFullPath, tnLH, result.PropID)
			if err != nil {
				golog.Error("Failed to create thumbnail",
					"propID", result.PropID,
					"error", err,
					"source", firstImageInfo.NewPath,
					"thumbnail", thumbnailFullPath,
					"hash", tnLH,
					"errorType", fmt.Sprintf("%T", err))
			} else {
				golog.Info("Thumbnail created successfully", "propID", result.PropID, "thumbnail", thumbnailFullPath)
			}
		}
	} else if len(successfulImagePaths) > 0 && len(imageInfos) > 0 && !config.DryRun && tnLH == 0 {
		golog.Warn("Skipping thumbnail creation because tnLH is 0", "propID", result.PropID, "imageCount", len(imageInfos))
	} else if len(successfulImagePaths) == 0 {
		golog.Info("Skipping thumbnail creation because no images were successfully processed", "propID", result.PropID)
	}

	// Update database if any images were successfully processed
	// Only update database if we have successfully processed images
	if len(successfulImagePaths) > 0 && result.Success > 0 {
		// Use the filtered phoLH (only successful images)
		updateParams := DBUpdateParams{
			Prop:  prop,
			PhoLH: phoLH, // This now contains only successfully processed images
			TnLH:  tnLH,
			Disk:  config.Disk,
			PhoP:  phoP,
		}

		if err := updateDB(updateParams); err != nil {
			return result, fmt.Errorf("failed to update database: %w", err)
		}

		golog.Info("Database updated successfully",
			"propID", result.PropID,
			"successfulImages", len(successfulImagePaths),
			"phoLHCount", len(phoLH))
	} else if len(successfulImagePaths) == 0 {
		golog.Info("Skipping database update because no images were successfully processed", "propID", result.PropID)
	}

	// Record migration result in log (success or partial failure)
	var status string
	var errMsg string

	totalExpected := len(imagePaths)
	successfulProcessed := len(successfulImagePaths)

	if successfulProcessed > 0 && result.Failed == 0 && successfulProcessed == totalExpected {
		status = "success"
	} else if successfulProcessed > 0 && (result.Failed > 0 || successfulProcessed < totalExpected) {
		status = "partial_success"
		errMsg = fmt.Sprintf("PropID %v: Expected %d files, successfully processed %d files, failed %d files",
			prop["_id"], totalExpected, successfulProcessed, result.Failed)
	} else if result.Failed > 0 && successfulProcessed == 0 {
		status = "all_files_failed"
		errMsg = fmt.Sprintf("PropID %v: All %d files failed to process", prop["_id"], totalExpected)
	} else {
		status = "no_files_found"
		errMsg = fmt.Sprintf("PropID %v: No image files found to process", prop["_id"])
	}

	migrationResult := MigrationResult{
		PropID:    fmt.Sprintf("%v", prop["_id"]),
		SrcFolder: getSrcFolderFromProp(prop),
		DstFolder: phoP,
		Status:    status,
		ErrMsg:    errMsg,
		Disk:      config.Disk,
	}

	if logErr := insertMigrationLog(migrationResult); logErr != nil {
		golog.Error("Failed to insert migration log", "error", logErr, "PropID", fmt.Sprintf("%v", prop["_id"]))
	}

	// Clean up source files only after successful database operations
	// Only cleanup for link mode - rename mode already moved the files
	if config.Mode == "link" && len(batchResult.SuccessfulOps) > 0 {
		if cleanupErr := cleanup(batchResult.SuccessfulOps, batchResult.ImageInfos); cleanupErr != nil {
			golog.Error("Failed to cleanup source files", "error", cleanupErr, "PropID", fmt.Sprintf("%v", prop["_id"]))
			// Don't return error here as the main operation was successful
		}
	} else if config.Mode == "rename" {
		golog.Info("Skipping cleanup for rename mode - files already moved", "PropID", fmt.Sprintf("%v", prop["_id"]))
	}

	// Set the generated phoP and collected image paths in result
	result.PhoP = phoP
	result.ImagePaths = successfulImagePaths // Only include successfully processed images

	return result, nil
}

// ensureUniqueHash ensures the hash is unique within the property by adding a suffix if needed
func ensureUniqueHash(originalHash int32, originalKey string, usedHashes map[int32]bool) int32 {
	// If hash is already unique, return it
	if !usedHashes[originalHash] {
		return originalHash
	}

	// Hash collision detected, try adding incremental suffixes
	counter := 1
	for {
		// Create new key with suffix
		newKey := fmt.Sprintf("%s_%d", originalKey, counter)
		newHash := levelStore.MurmurToInt32(newKey)

		// If this new hash is unique, use it
		if !usedHashes[newHash] {
			golog.Info("Hash collision resolved",
				"originalKey", originalKey,
				"originalHash", originalHash,
				"newKey", newKey,
				"newHash", newHash,
				"counter", counter)
			return newHash
		}

		counter++

		// Safety check to prevent infinite loop (very unlikely but good practice)
		if counter > 1000 {
			golog.Error("Too many hash collisions",
				"originalKey", originalKey,
				"originalHash", originalHash,
				"counter", counter)
			panic(fmt.Sprintf("Failed to resolve hash collision after %d attempts for key: %s", counter, originalKey))
		}
	}
}

// collectTRBDDFImagePaths collects all image relative paths for TRB/DDF without processing files
func collectTRBDDFImagePaths(prop bson.M) ([]string, error) {
	phoCount := getIntValue(prop, "pho")
	if phoCount <= 0 {
		return nil, fmt.Errorf("invalid pho count: %d", phoCount)
	}

	var imagePaths []string

	// Collect each image path (numbered from 1 to phoCount)
	for i := 1; i <= phoCount; i++ {
		params := PathBuildParams{
			Prop:     prop,
			Src:      prop["src"].(string),
			Sid:      prop["sid"].(string),
			ImageNum: i,
		}

		relativePath, err := buildOriginalPath(params)
		if err != nil {
			golog.Error("Failed to build original path", "propID", prop["_id"], "imageNum", i, "error", err)
			continue // Skip this image but continue with others
		}

		imagePaths = append(imagePaths, relativePath)
	}

	return imagePaths, nil
}

// collectOTWCLGImagePaths collects all image relative paths for OTW/CLG without processing files
func collectOTWCLGImagePaths(prop bson.M) ([]string, error) {
	phoIDs, ok := prop["phoIDs"]
	if !ok {
		return nil, fmt.Errorf("phoIDs field not found")
	}

	// Handle different types of phoIDs
	var imageIDs []interface{}
	switch v := phoIDs.(type) {
	case []interface{}:
		imageIDs = v
	case []string:
		for _, id := range v {
			imageIDs = append(imageIDs, id)
		}
	case []int:
		for _, id := range v {
			imageIDs = append(imageIDs, id)
		}
	case bson.A: // MongoDB BSON array type
		imageIDs = []interface{}(v)
	default:
		return nil, fmt.Errorf("invalid phoIDs field type: %T, value: %v", phoIDs, phoIDs)
	}

	if len(imageIDs) == 0 {
		return nil, fmt.Errorf("empty phoIDs array")
	}

	var imagePaths []string

	// Collect each image path
	for i, imageID := range imageIDs {
		params := PathBuildParams{
			Prop:    prop,
			Src:     prop["src"].(string),
			Sid:     prop["sid"].(string),
			ImageID: imageID, // buildOriginalPath will handle type conversion
		}

		relativePath, err := buildOriginalPath(params)
		if err != nil {
			golog.Error("Failed to build original path", "propID", prop["_id"], "imageIndex", i, "imageID", imageID, "error", err)
			continue // Skip this image but continue with others
		}

		imagePaths = append(imagePaths, relativePath)
	}

	return imagePaths, nil
}

// generateNewPhoP generates a new phoP path for the property
func generateNewPhoP(prop bson.M) (string, error) {
	propID := fmt.Sprintf("%v", prop["_id"])

	// Handle different integer types from MongoDB
	var onD int
	switch v := prop["onD"].(type) {
	case int:
		onD = v
	case int32:
		onD = int(v)
	case int64:
		onD = int(v)
	default:
		return "", fmt.Errorf("missing or invalid OnD field type in property %s: %T", propID, prop["onD"])
	}

	src, ok := prop["src"].(string)
	if !ok {
		return "", fmt.Errorf("missing or invalid src field in property %s", propID)
	}

	sid, ok := prop["sid"].(string)
	if !ok {
		return "", fmt.Errorf("missing or invalid sid field in property %s", propID)
	}

	ts := gohelper.DateToTime(onD)
	filePath, err := levelStore.GetFullFilePathForProp(ts, src, sid)
	if err != nil {
		return "", fmt.Errorf("failed to generate phoP for property %s: %w", propID, err)
	}

	// Normalize phoP to remove leading slash for consistency
	normalizedPath := goresodownload.NormalizePhoP(filePath)
	golog.Info("Generated new phoP", "propID", propID, "phoP", normalizedPath)
	return normalizedPath, nil
}

// getSrcFolder returns the old relative folder path from imagePaths[0]
func getSrcFolder(imagePaths []string) string {
	if len(imagePaths) == 0 {
		return ""
	}

	firstImagePath := imagePaths[0]
	return extractSrcFolderFromPath(firstImagePath)
}

// getDstFolder returns the new relative folder path from phoP
func getDstFolder(phoP string) string {
	if phoP == "" {
		return ""
	}

	// Extract directory part from phoP: "/L1/L2/filename" -> "/L1/L2"
	if strings.Contains(phoP, "/") {
		parts := strings.Split(phoP, "/")
		if len(parts) >= 3 {
			// Return /L1/L2 format (new relative folder)
			return "/" + parts[1] + "/" + parts[2]
		}
	}

	return phoP
}

// getSrcFolderFromProp returns the old relative folder path from property (fallback for error cases)
func getSrcFolderFromProp(prop bson.M) string {
	// Build the source folder from property data by constructing a sample image path
	propID := fmt.Sprintf("%v", prop["_id"])
	src, ok := prop["src"].(string)
	if !ok {
		golog.Error("Invalid src field for getSrcFolderFromProp", "propID", propID)
		return ""
	}

	// Try to build a sample image path to extract the source folder
	switch src {
	case SRC_TRB, SRC_DDF:
		// For TRB/DDF, try to build the first image path
		if imagePath, err := buildSampleImagePath(prop, src, 1); err == nil {
			return extractSrcFolderFromPath(imagePath)
		}
	case SRC_OTW, SRC_CLG:
		// For OTW/CLG, try to build path with a sample imageID
		if imagePath, err := buildSampleImagePath(prop, src, "1"); err == nil {
			return extractSrcFolderFromPath(imagePath)
		}
	}

	golog.Error("Failed to extract source folder from property", "propID", propID, "src", src)
	return ""
}

// extractSrcFolderFromPath extracts the source folder from various path formats
func extractSrcFolderFromPath(imagePath string) string {
	if imagePath == "" || !strings.Contains(imagePath, "/") {
		return imagePath
	}

	// Extract directory part by removing the filename
	// DDF: crea/ddf/img/669/26226669_1.jpg -> /crea/ddf/img/669
	// OTW: oreb/mls/80/61/731271_1.jpg -> /oreb/mls/80/61
	// CLG: creb/mls/80/61/731271_1.jpg -> /creb/mls/80/61
	// TRB: treb/mls/1/123/abc123.jpg -> /treb/mls/1/123

	lastSlashIndex := strings.LastIndex(imagePath, "/")
	if lastSlashIndex > 0 {
		// Return the directory part with leading slash
		return "/" + imagePath[:lastSlashIndex]
	} else if lastSlashIndex == 0 {
		// Path starts with slash, return everything except filename
		return imagePath[:lastSlashIndex+1]
	}

	return imagePath
}

// buildSampleImagePath builds a sample image path for extracting source folder
func buildSampleImagePath(prop bson.M, src string, imageRef interface{}) (string, error) {
	sid, ok := prop["sid"].(string)
	if !ok {
		return "", fmt.Errorf("invalid sid field")
	}

	params := PathBuildParams{
		Prop: prop,
		Src:  src,
		Sid:  sid,
	}

	switch src {
	case SRC_TRB, SRC_DDF:
		if imageNum, ok := imageRef.(int); ok {
			params.ImageNum = imageNum
		} else {
			params.ImageNum = 1
		}
	case SRC_OTW, SRC_CLG:
		params.ImageID = imageRef
	default:
		return "", fmt.Errorf("unsupported src type: %s", src)
	}

	return buildOriginalPath(params)
}

// getDstFolderFromProp returns the new relative folder path from property (fallback for error cases)
func getDstFolderFromProp(prop bson.M) string {
	// Try to generate new phoP path
	propID := fmt.Sprintf("%v", prop["_id"])
	src, ok := prop["src"].(string)
	if !ok {
		golog.Error("Invalid src field for getDstFolderFromProp", "propID", propID)
		return ""
	}

	// Handle different integer types from MongoDB
	var onD int
	switch v := prop["onD"].(type) {
	case int:
		onD = v
	case int32:
		onD = int(v)
	case int64:
		onD = int(v)
	default:
		golog.Error("Invalid onD field type for getDstFolderFromProp", "propID", propID, "type", fmt.Sprintf("%T", prop["onD"]), "value", prop["onD"])
		return ""
	}

	sid, ok := prop["sid"].(string)
	if !ok {
		golog.Error("Invalid sid field for getDstFolderFromProp", "propID", propID)
		return ""
	}

	ts := gohelper.DateToTime(onD)
	newFilePath, err := levelStore.GetFullFilePathForProp(ts, src, sid)
	if err != nil {
		golog.Error("Failed to generate destination folder from prop", "propID", propID, "error", err)
		return ""
	}

	// Extract directory part from new path: "/L1/L2/filename" -> "/L1/L2"
	if strings.Contains(newFilePath, "/") {
		parts := strings.Split(newFilePath, "/")
		if len(parts) >= 3 {
			// Return /L1/L2 format (new relative folder)
			return "/" + parts[1] + "/" + parts[2]
		}
	}

	return newFilePath
}

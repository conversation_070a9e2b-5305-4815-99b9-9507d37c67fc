package gospeedmeter

import (
	"strings"
	"testing"
	"time"
)

func TestNewSpeedAggregate(t *testing.T) {
	options := SpeedAggregateOptions{
		WindowDuration:  30 * time.Second,
		CleanupInterval: 5 * time.Second,
		MinDataPoints:   3,
	}
	sa := NewSpeedAggregate(options)
	defer sa.Stop()

	if sa == nil {
		t.Error("Expected non-nil SpeedAggregate")
	}
	if sa.windowDuration != 30*time.Second {
		t.Errorf("Expected window duration 30s, got %v", sa.windowDuration)
	}
	if sa.minDataPoints != 3 {
		t.Errorf("Expected min data points 3, got %d", sa.minDataPoints)
	}
}

func TestSpeedAggregateAddDataPoint(t *testing.T) {
	sa := NewSpeedAggregate(SpeedAggregateOptions{})
	defer sa.Stop()

	// 添加数据点
	sa.AddDataPoint("downloads", 100)
	sa.AddDataPoint("downloads", 200)
	sa.AddDataPoint("writes", 50)

	// 检查数据点计数
	counts := sa.GetDataPointCount()
	if counts["downloads"] != 2 {
		t.Errorf("Expected 2 data points for downloads, got %d", counts["downloads"])
	}
	if counts["writes"] != 1 {
		t.Errorf("Expected 1 data point for writes, got %d", counts["writes"])
	}

	// 检查总计数器
	counters := sa.GetTotalCounters()
	if counters["downloads"] != 300 {
		t.Errorf("Expected total downloads 300, got %f", counters["downloads"])
	}
	if counters["writes"] != 50 {
		t.Errorf("Expected total writes 50, got %f", counters["writes"])
	}
}

func TestSpeedAggregateGetCurrentSpeed(t *testing.T) {
	sa := NewSpeedAggregate(SpeedAggregateOptions{
		WindowDuration: 5 * time.Second,
		MinDataPoints:  2,
	})
	defer sa.Stop()

	// 添加第一个数据点
	sa.AddDataPoint("downloads", 1000)

	// 等待一小段时间
	time.Sleep(100 * time.Millisecond)

	// 添加第二个数据点
	sa.AddDataPoint("downloads", 1000)

	// 获取速度
	speed := sa.GetCurrentSpeed("downloads", UnitS)

	// 速度应该大于0（大约10000/s，因为2000字节在0.1秒内）
	if speed <= 0 {
		t.Errorf("Expected speed > 0, got %f", speed)
	}

	// 测试不存在的指标
	speed = sa.GetCurrentSpeed("nonexistent", UnitS)
	if speed != 0 {
		t.Errorf("Expected speed 0 for nonexistent metric, got %f", speed)
	}
}

func TestSpeedAggregateGetAllSpeeds(t *testing.T) {
	sa := NewSpeedAggregate(SpeedAggregateOptions{
		WindowDuration: 5 * time.Second,
		MinDataPoints:  2,
	})
	defer sa.Stop()

	// 添加多个指标的数据点
	sa.AddDataPoint("downloads", 500)
	sa.AddDataPoint("writes", 300)

	time.Sleep(50 * time.Millisecond)

	sa.AddDataPoint("downloads", 500)
	sa.AddDataPoint("writes", 300)

	// 获取所有速度
	speeds := sa.GetAllSpeeds(UnitS)

	if len(speeds) != 2 {
		t.Errorf("Expected 2 metrics, got %d", len(speeds))
	}

	if speeds["downloads"] <= 0 {
		t.Errorf("Expected downloads speed > 0, got %f", speeds["downloads"])
	}

	if speeds["writes"] <= 0 {
		t.Errorf("Expected writes speed > 0, got %f", speeds["writes"])
	}
}

func TestSpeedAggregateCleanupOldData(t *testing.T) {
	sa := NewSpeedAggregate(SpeedAggregateOptions{
		WindowDuration:  100 * time.Millisecond, // 很短的窗口用于测试
		CleanupInterval: 50 * time.Millisecond,  // 很短的清理间隔
		MinDataPoints:   1,
	})
	defer sa.Stop()

	// 添加数据点
	sa.AddDataPoint("test", 100)

	// 检查数据点存在
	counts := sa.GetDataPointCount()
	if counts["test"] != 1 {
		t.Errorf("Expected 1 data point, got %d", counts["test"])
	}

	// 等待超过窗口时间
	time.Sleep(150 * time.Millisecond)

	// 手动触发清理或添加新数据点
	sa.cleanupOldData()

	// 检查旧数据点被清理
	counts = sa.GetDataPointCount()
	if counts["test"] != 0 {
		t.Errorf("Expected old data points to be cleaned up, got %d points", counts["test"])
	}
}

func TestSpeedAggregateToString(t *testing.T) {
	sa := NewSpeedAggregate(SpeedAggregateOptions{
		MinDataPoints: 2,
	})
	defer sa.Stop()

	// 添加数据点
	sa.AddDataPoint("downloads", 1000)
	time.Sleep(50 * time.Millisecond)
	sa.AddDataPoint("downloads", 1000)
	sa.AddDataPoint("writes", 500)
	time.Sleep(50 * time.Millisecond)
	sa.AddDataPoint("writes", 500)

	// 获取字符串表示
	result := sa.ToString(UnitS, nil)

	// 检查结果包含预期内容
	if !strings.Contains(result, "downloads") {
		t.Error("Expected result to contain 'downloads'")
	}
	if !strings.Contains(result, "writes") {
		t.Error("Expected result to contain 'writes'")
	}
	if !strings.Contains(result, "[sliding]") {
		t.Error("Expected result to contain '[sliding]' marker")
	}
}

func TestSpeedAggregateWithEstimation(t *testing.T) {
	sa := NewSpeedAggregate(SpeedAggregateOptions{
		MinDataPoints: 2,
	})
	defer sa.Stop()

	// 添加数据点
	sa.AddDataPoint("downloads", 1000)
	time.Sleep(50 * time.Millisecond)
	sa.AddDataPoint("downloads", 1000)

	// 使用估算目标
	toBeEstimated := map[string]float64{
		"downloads": 10000, // 目标10000
	}

	result := sa.ToString(UnitS, toBeEstimated)

	// 检查结果包含估算信息
	if !strings.Contains(result, "est:") {
		t.Error("Expected result to contain estimation 'est:'")
	}
}

func TestSpeedAggregateReset(t *testing.T) {
	sa := NewSpeedAggregate(SpeedAggregateOptions{})
	defer sa.Stop()

	// 添加数据点
	sa.AddDataPoint("test", 100)
	sa.AddDataPoint("test", 200)

	// 检查数据存在
	counts := sa.GetDataPointCount()
	if counts["test"] != 2 {
		t.Errorf("Expected 2 data points before reset, got %d", counts["test"])
	}

	// 重置
	sa.Reset()

	// 检查数据被清空
	counts = sa.GetDataPointCount()
	if counts["test"] != 0 {
		t.Errorf("Expected 0 data points after reset, got %d", counts["test"])
	}

	counters := sa.GetTotalCounters()
	if counters["test"] != 0 {
		t.Errorf("Expected 0 total counter after reset, got %f", counters["test"])
	}
}

func TestSpeedAggregateMinDataPoints(t *testing.T) {
	sa := NewSpeedAggregate(SpeedAggregateOptions{
		MinDataPoints: 3, // 需要至少3个数据点
	})
	defer sa.Stop()

	// 只添加2个数据点
	sa.AddDataPoint("test", 100)
	time.Sleep(50 * time.Millisecond)
	sa.AddDataPoint("test", 100)

	// 速度应该为0，因为数据点不足
	speed := sa.GetCurrentSpeed("test", UnitS)
	if speed != 0 {
		t.Errorf("Expected speed 0 with insufficient data points, got %f", speed)
	}

	// 添加第3个数据点
	time.Sleep(50 * time.Millisecond)
	sa.AddDataPoint("test", 100)

	// 现在应该有速度
	speed = sa.GetCurrentSpeed("test", UnitS)
	if speed <= 0 {
		t.Errorf("Expected speed > 0 with sufficient data points, got %f", speed)
	}
}

func TestSpeedAggregateCallbackTrigger(t *testing.T) {
	callbackTriggered := false
	var callbackResult string

	sa := NewSpeedAggregate(SpeedAggregateOptions{
		IntervalTriggerCount: 3, // 每3个数据点触发一次
		IntervalCallback: func(sa *SpeedAggregate) {
			callbackTriggered = true
			callbackResult = sa.ToString(UnitS, nil)
		},
	})
	defer sa.Stop()

	// 添加3个数据点应该触发回调
	sa.AddDataPoint("test", 100)
	sa.AddDataPoint("test", 100)
	sa.AddDataPoint("test", 100)

	// 等待回调执行
	time.Sleep(10 * time.Millisecond)

	if !callbackTriggered {
		t.Error("Expected callback to be triggered after 3 data points")
	}

	if callbackResult == "" {
		t.Error("Expected callback result to be non-empty")
	}
}

func TestSpeedAggregateStop(t *testing.T) {
	sa := NewSpeedAggregate(SpeedAggregateOptions{
		CleanupInterval: 10 * time.Millisecond,
	})

	// 检查清理例程正在运行
	if !sa.cleanupRunning {
		t.Error("Expected cleanup routine to be running")
	}

	// 停止
	sa.Stop()

	// 检查清理例程已停止
	if sa.cleanupRunning {
		t.Error("Expected cleanup routine to be stopped")
	}
}

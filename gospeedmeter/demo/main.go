package main

import (
	"fmt"
	"math/rand"
	"sync"
	"time"

	"github.com/real-rm/gospeedmeter"
)

func main() {
	fmt.Println("=== SpeedAggregate 滑动窗口速度聚合模块演示 ===\n")

	// 创建 SpeedAggregate 实例
	sa := gospeedmeter.NewSpeedAggregate(gospeedmeter.SpeedAggregateOptions{
		WindowDuration:       10 * time.Second, // 10秒滑动窗口
		CleanupInterval:      2 * time.Second,  // 每2秒清理一次
		IntervalTriggerCount: 20,               // 每20个数据点打印一次
		MinDataPoints:        3,                // 至少3个数据点才计算速度
		IntervalCallback: func(sa *gospeedmeter.SpeedAggregate) {
			fmt.Printf("📊 定期报告: %s\n", sa.ToString(gospeedmeter.UnitS, nil))
		},
	})
	defer sa.Stop()

	// 同时创建传统的 SpeedMeter 进行对比
	sm := gospeedmeter.NewSpeedMeter(gospeedmeter.SpeedMeterOptions{
		IntervalTriggerCount: 20,
		IntervalCallback: func(sm *gospeedmeter.SpeedMeter) {
			fmt.Printf("📈 累计平均: %s\n", sm.ToString(gospeedmeter.UnitS, nil))
		},
	})

	fmt.Println("🚀 开始模拟多线程下载场景...")
	fmt.Println("   - 3个下载 worker")
	fmt.Println("   - 每个 worker 模拟不同的下载速度变化")
	fmt.Println("   - 滑动窗口: 10秒")
	fmt.Println("   - 对比显示滑动窗口速度 vs 累计平均速度\n")

	var wg sync.WaitGroup

	// 启动3个模拟下载 worker
	for workerID := 0; workerID < 3; workerID++ {
		wg.Add(1)
		go func(id int) {
			defer wg.Done()
			simulateDownloadWorker(id, sa, sm)
		}(workerID)
	}

	// 启动监控 goroutine
	go monitorSpeeds(sa, sm)

	// 等待所有 worker 完成
	wg.Wait()

	fmt.Println("\n🏁 下载完成，最终报告:")
	fmt.Printf("滑动窗口速度: %s\n", sa.ToString(gospeedmeter.UnitS, nil))
	fmt.Printf("累计平均速度: %s\n", sm.ToString(gospeedmeter.UnitS, nil))

	// 显示数据点统计
	counts := sa.GetDataPointCount()
	counters := sa.GetTotalCounters()
	fmt.Println("\n📋 数据点统计:")
	for name, count := range counts {
		fmt.Printf("  %s: %d 个数据点, 总计 %.0f\n", name, count, counters[name])
	}
}

// simulateDownloadWorker 模拟一个下载 worker
func simulateDownloadWorker(workerID int, sa *gospeedmeter.SpeedAggregate, sm *gospeedmeter.SpeedMeter) {
	rand.Seed(time.Now().UnixNano() + int64(workerID))

	// 每个 worker 运行30秒
	endTime := time.Now().Add(30 * time.Second)
	iteration := 0

	for time.Now().Before(endTime) {
		iteration++

		// 模拟不同阶段的下载速度变化
		var downloadBytes, writeBytes float64

		switch {
		case iteration < 10:
			// 初始阶段：较慢的速度
			downloadBytes = float64(rand.Intn(50000) + 10000) // 10KB-60KB
			writeBytes = downloadBytes * 0.8
		case iteration < 20:
			// 加速阶段：速度提升
			downloadBytes = float64(rand.Intn(100000) + 50000) // 50KB-150KB
			writeBytes = downloadBytes * 0.85
		case iteration < 25:
			// 高速阶段：最快速度
			downloadBytes = float64(rand.Intn(200000) + 100000) // 100KB-300KB
			writeBytes = downloadBytes * 0.9
		default:
			// 减速阶段：速度下降
			downloadBytes = float64(rand.Intn(80000) + 20000) // 20KB-100KB
			writeBytes = downloadBytes * 0.75
		}

		// 记录到两个速度计算器
		sa.AddDataPoint("downloadBytes", downloadBytes)
		sa.AddDataPoint("writeBytes", writeBytes)
		sa.AddDataPoint("downloadCount", 1)

		sm.Check("downloadBytes", downloadBytes)
		sm.Check("writeBytes", writeBytes)
		sm.Check("downloadCount", 1)

		// 模拟网络延迟和处理时间
		sleepTime := time.Duration(rand.Intn(200)+100) * time.Millisecond
		time.Sleep(sleepTime)
	}

	fmt.Printf("✅ Worker %d 完成\n", workerID)
}

// monitorSpeeds 监控速度变化
func monitorSpeeds(sa *gospeedmeter.SpeedAggregate, sm *gospeedmeter.SpeedMeter) {
	ticker := time.NewTicker(5 * time.Second)
	defer ticker.Stop()

	for i := 0; i < 6; i++ { // 监控30秒
		<-ticker.C

		// 获取当前速度
		slidingSpeeds := sa.GetAllSpeeds(gospeedmeter.UnitS)
		avgSpeeds := sm.GetSpeed(gospeedmeter.UnitS)

		fmt.Printf("\n⏰ %d秒监控报告:\n", (i+1)*5)
		fmt.Printf("  滑动窗口下载速度: %.0f bytes/s\n", slidingSpeeds["downloadBytes"])
		fmt.Printf("  累计平均下载速度: %.0f bytes/s\n", avgSpeeds["downloadBytes"])
		fmt.Printf("  滑动窗口写入速度: %.0f bytes/s\n", slidingSpeeds["writeBytes"])
		fmt.Printf("  累计平均写入速度: %.0f bytes/s\n", avgSpeeds["writeBytes"])

		// 显示速度差异
		if slidingSpeeds["downloadBytes"] > 0 && avgSpeeds["downloadBytes"] > 0 {
			ratio := slidingSpeeds["downloadBytes"] / avgSpeeds["downloadBytes"]
			if ratio > 1.1 {
				fmt.Printf("  📈 当前下载速度比平均速度快 %.1f%%\n", (ratio-1)*100)
			} else if ratio < 0.9 {
				fmt.Printf("  📉 当前下载速度比平均速度慢 %.1f%%\n", (1-ratio)*100)
			} else {
				fmt.Printf("  ➡️  当前下载速度接近平均水平\n")
			}
		}
	}
}
